"""
A.T.L.A.S. Risk Management Agent
Specialized agent for VaR calculations, position sizing, and comprehensive risk metrics
"""

import asyncio
import logging
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import math

# CrewAI imports
from crewai.tools import BaseTool

# Core imports
from atlas_multi_agent_core import AtlasBaseAgent, AgentRole, MultiAgentTask
from atlas_risk_core import AtlasRiskEngine
from models import RiskAssessment, Position

logger = logging.getLogger(__name__)

# ============================================================================
# RISK MANAGEMENT TOOLS
# ============================================================================

class VaRCalculationTool(BaseTool):
    """Tool for Value at Risk (VaR) calculations using multiple methods"""
    
    name: str = "var_calculator"
    description: str = "Calculates Value at Risk using historical, parametric, and Monte Carlo methods"
    
    def __init__(self, risk_engine: AtlasRiskEngine):
        super().__init__()
        self.risk_engine = risk_engine
    
    def _run(self, portfolio_data: Dict[str, Any], confidence_level: float = 0.95, 
             time_horizon: int = 1, method: str = "historical") -> Dict[str, Any]:
        """Calculate VaR using specified method"""
        try:
            if method == "historical":
                return self._historical_var(portfolio_data, confidence_level, time_horizon)
            elif method == "parametric":
                return self._parametric_var(portfolio_data, confidence_level, time_horizon)
            elif method == "monte_carlo":
                return self._monte_carlo_var(portfolio_data, confidence_level, time_horizon)
            else:
                return {"error": f"Unsupported VaR method: {method}"}
        except Exception as e:
            return {"error": str(e)}
    
    def _historical_var(self, portfolio_data: Dict[str, Any], confidence_level: float, time_horizon: int) -> Dict[str, Any]:
        """Calculate Historical VaR"""
        portfolio_value = portfolio_data.get("total_value", 100000)
        
        # Mock historical returns (in practice, would use actual historical data)
        historical_returns = np.random.normal(0.001, 0.02, 252)  # Daily returns for 1 year
        
        # Calculate VaR
        var_percentile = (1 - confidence_level) * 100
        var_return = np.percentile(historical_returns, var_percentile)
        var_amount = portfolio_value * var_return * math.sqrt(time_horizon)
        
        return {
            "method": "historical",
            "var_amount": abs(var_amount),
            "var_percentage": abs(var_return) * 100,
            "confidence_level": confidence_level,
            "time_horizon_days": time_horizon,
            "portfolio_value": portfolio_value,
            "calculation_timestamp": datetime.now().isoformat()
        }
    
    def _parametric_var(self, portfolio_data: Dict[str, Any], confidence_level: float, time_horizon: int) -> Dict[str, Any]:
        """Calculate Parametric VaR"""
        portfolio_value = portfolio_data.get("total_value", 100000)
        portfolio_volatility = portfolio_data.get("volatility", 0.02)  # Daily volatility
        
        # Calculate z-score for confidence level
        from scipy.stats import norm
        z_score = norm.ppf(1 - confidence_level)
        
        # Calculate VaR
        var_amount = portfolio_value * portfolio_volatility * z_score * math.sqrt(time_horizon)
        
        return {
            "method": "parametric",
            "var_amount": abs(var_amount),
            "var_percentage": abs(portfolio_volatility * z_score) * 100,
            "confidence_level": confidence_level,
            "time_horizon_days": time_horizon,
            "portfolio_value": portfolio_value,
            "portfolio_volatility": portfolio_volatility,
            "calculation_timestamp": datetime.now().isoformat()
        }
    
    def _monte_carlo_var(self, portfolio_data: Dict[str, Any], confidence_level: float, time_horizon: int) -> Dict[str, Any]:
        """Calculate Monte Carlo VaR"""
        portfolio_value = portfolio_data.get("total_value", 100000)
        num_simulations = 10000
        
        # Mock Monte Carlo simulation
        simulated_returns = np.random.normal(0.001, 0.02, num_simulations)
        simulated_values = portfolio_value * (1 + simulated_returns * math.sqrt(time_horizon))
        portfolio_changes = simulated_values - portfolio_value
        
        # Calculate VaR
        var_percentile = (1 - confidence_level) * 100
        var_amount = abs(np.percentile(portfolio_changes, var_percentile))
        
        return {
            "method": "monte_carlo",
            "var_amount": var_amount,
            "var_percentage": (var_amount / portfolio_value) * 100,
            "confidence_level": confidence_level,
            "time_horizon_days": time_horizon,
            "portfolio_value": portfolio_value,
            "num_simulations": num_simulations,
            "calculation_timestamp": datetime.now().isoformat()
        }

class PositionSizingTool(BaseTool):
    """Tool for optimal position sizing calculations"""
    
    name: str = "position_sizer"
    description: str = "Calculates optimal position sizes using various risk management methods"
    
    def _run(self, trade_data: Dict[str, Any], risk_parameters: Dict[str, Any], 
             method: str = "fixed_fractional") -> Dict[str, Any]:
        """Calculate optimal position size"""
        try:
            if method == "fixed_fractional":
                return self._fixed_fractional_sizing(trade_data, risk_parameters)
            elif method == "kelly_criterion":
                return self._kelly_criterion_sizing(trade_data, risk_parameters)
            elif method == "volatility_adjusted":
                return self._volatility_adjusted_sizing(trade_data, risk_parameters)
            else:
                return {"error": f"Unsupported position sizing method: {method}"}
        except Exception as e:
            return {"error": str(e)}
    
    def _fixed_fractional_sizing(self, trade_data: Dict[str, Any], risk_parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate position size using fixed fractional method"""
        account_value = risk_parameters.get("account_value", 100000)
        risk_per_trade = risk_parameters.get("risk_per_trade", 0.02)  # 2% risk per trade
        entry_price = trade_data.get("entry_price", 100)
        stop_loss = trade_data.get("stop_loss", 95)
        
        # Calculate risk per share
        risk_per_share = abs(entry_price - stop_loss)
        
        # Calculate position size
        risk_amount = account_value * risk_per_trade
        position_size = int(risk_amount / risk_per_share) if risk_per_share > 0 else 0
        position_value = position_size * entry_price
        
        return {
            "method": "fixed_fractional",
            "position_size": position_size,
            "position_value": position_value,
            "risk_amount": risk_amount,
            "risk_per_share": risk_per_share,
            "risk_percentage": risk_per_trade * 100,
            "calculation_timestamp": datetime.now().isoformat()
        }
    
    def _kelly_criterion_sizing(self, trade_data: Dict[str, Any], risk_parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate position size using Kelly Criterion"""
        win_probability = trade_data.get("win_probability", 0.6)
        avg_win = trade_data.get("avg_win", 0.05)
        avg_loss = trade_data.get("avg_loss", 0.03)
        account_value = risk_parameters.get("account_value", 100000)
        
        # Kelly formula: f = (bp - q) / b
        # where b = avg_win/avg_loss, p = win_probability, q = 1-p
        b = avg_win / avg_loss if avg_loss > 0 else 1
        p = win_probability
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b if b > 0 else 0
        kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Cap at 25% for safety
        
        position_value = account_value * kelly_fraction
        entry_price = trade_data.get("entry_price", 100)
        position_size = int(position_value / entry_price) if entry_price > 0 else 0
        
        return {
            "method": "kelly_criterion",
            "position_size": position_size,
            "position_value": position_value,
            "kelly_fraction": kelly_fraction,
            "win_probability": win_probability,
            "risk_reward_ratio": b,
            "calculation_timestamp": datetime.now().isoformat()
        }
    
    def _volatility_adjusted_sizing(self, trade_data: Dict[str, Any], risk_parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate position size adjusted for volatility"""
        account_value = risk_parameters.get("account_value", 100000)
        target_volatility = risk_parameters.get("target_volatility", 0.15)  # 15% annual volatility
        asset_volatility = trade_data.get("asset_volatility", 0.25)  # 25% annual volatility
        entry_price = trade_data.get("entry_price", 100)
        
        # Adjust position size based on volatility
        volatility_adjustment = target_volatility / asset_volatility if asset_volatility > 0 else 1
        base_allocation = 0.1  # 10% base allocation
        adjusted_allocation = base_allocation * volatility_adjustment
        
        position_value = account_value * adjusted_allocation
        position_size = int(position_value / entry_price) if entry_price > 0 else 0
        
        return {
            "method": "volatility_adjusted",
            "position_size": position_size,
            "position_value": position_value,
            "volatility_adjustment": volatility_adjustment,
            "target_volatility": target_volatility,
            "asset_volatility": asset_volatility,
            "adjusted_allocation": adjusted_allocation,
            "calculation_timestamp": datetime.now().isoformat()
        }

class RiskMetricsTool(BaseTool):
    """Tool for calculating comprehensive risk metrics"""
    
    name: str = "risk_metrics"
    description: str = "Calculates comprehensive risk metrics including Sharpe ratio, max drawdown, etc."
    
    def _run(self, portfolio_data: Dict[str, Any], benchmark_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Calculate comprehensive risk metrics"""
        try:
            # Mock portfolio returns (in practice, would use actual data)
            portfolio_returns = np.random.normal(0.001, 0.02, 252)  # Daily returns
            benchmark_returns = np.random.normal(0.0008, 0.015, 252) if benchmark_data else None
            
            metrics = {}
            
            # Calculate Sharpe Ratio
            risk_free_rate = 0.02 / 252  # Daily risk-free rate
            excess_returns = portfolio_returns - risk_free_rate
            sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
            metrics["sharpe_ratio"] = sharpe_ratio
            
            # Calculate Maximum Drawdown
            cumulative_returns = np.cumprod(1 + portfolio_returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdowns)
            metrics["max_drawdown"] = abs(max_drawdown)
            
            # Calculate Volatility
            annual_volatility = np.std(portfolio_returns) * np.sqrt(252)
            metrics["annual_volatility"] = annual_volatility
            
            # Calculate Beta (if benchmark provided)
            if benchmark_returns is not None:
                covariance = np.cov(portfolio_returns, benchmark_returns)[0, 1]
                benchmark_variance = np.var(benchmark_returns)
                beta = covariance / benchmark_variance if benchmark_variance > 0 else 1
                metrics["beta"] = beta
                
                # Calculate Alpha
                benchmark_return = np.mean(benchmark_returns) * 252
                portfolio_return = np.mean(portfolio_returns) * 252
                alpha = portfolio_return - (risk_free_rate * 252 + beta * (benchmark_return - risk_free_rate * 252))
                metrics["alpha"] = alpha
            
            # Calculate Sortino Ratio
            downside_returns = portfolio_returns[portfolio_returns < 0]
            downside_deviation = np.std(downside_returns) * np.sqrt(252) if len(downside_returns) > 0 else 0
            sortino_ratio = (np.mean(portfolio_returns) * 252 - risk_free_rate * 252) / downside_deviation if downside_deviation > 0 else 0
            metrics["sortino_ratio"] = sortino_ratio
            
            # Calculate Calmar Ratio
            annual_return = np.mean(portfolio_returns) * 252
            calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0
            metrics["calmar_ratio"] = calmar_ratio
            
            return {
                "risk_metrics": metrics,
                "calculation_period_days": len(portfolio_returns),
                "calculation_timestamp": datetime.now().isoformat(),
                "risk_assessment": self._assess_risk_level(metrics)
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def _assess_risk_level(self, metrics: Dict[str, float]) -> str:
        """Assess overall risk level based on metrics"""
        sharpe_ratio = metrics.get("sharpe_ratio", 0)
        max_drawdown = metrics.get("max_drawdown", 0)
        volatility = metrics.get("annual_volatility", 0)
        
        risk_score = 0
        
        # Sharpe ratio assessment
        if sharpe_ratio > 1.5:
            risk_score += 1
        elif sharpe_ratio > 1.0:
            risk_score += 0.5
        
        # Max drawdown assessment
        if max_drawdown < 0.1:
            risk_score += 1
        elif max_drawdown < 0.2:
            risk_score += 0.5
        
        # Volatility assessment
        if volatility < 0.15:
            risk_score += 1
        elif volatility < 0.25:
            risk_score += 0.5
        
        if risk_score >= 2.5:
            return "low_risk"
        elif risk_score >= 1.5:
            return "moderate_risk"
        else:
            return "high_risk"

# ============================================================================
# RISK MANAGEMENT AGENT
# ============================================================================

class AtlasRiskManagementAgent(AtlasBaseAgent):
    """Specialized agent for comprehensive risk management"""
    
    def __init__(self, agent_id: str = None, config: Dict[str, Any] = None):
        agent_id = agent_id or f"risk_manager_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        super().__init__(agent_id, AgentRole.RISK_MANAGER, config)
        
        self.risk_engine = None
        self.risk_limits = {
            "max_position_size": 0.1,  # 10% of portfolio
            "max_sector_exposure": 0.3,  # 30% in any sector
            "max_daily_var": 0.02,  # 2% daily VaR
            "min_sharpe_ratio": 1.0,
            "max_drawdown": 0.15  # 15% maximum drawdown
        }
    
    async def _initialize_tools(self):
        """Initialize risk management specific tools"""
        try:
            # Initialize risk engine
            self.risk_engine = AtlasRiskEngine()
            await self.risk_engine.initialize()
            
            # Initialize risk management tools
            self.tools = [
                VaRCalculationTool(self.risk_engine),
                PositionSizingTool(),
                RiskMetricsTool()
            ]
            
            self.logger.info(f"Initialized {len(self.tools)} risk management tools")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize risk management tools: {e}")
            raise
    
    def _get_agent_goal(self) -> str:
        """Get the risk management agent's primary goal"""
        return ("Ensure optimal risk management through comprehensive VaR calculations, "
                "intelligent position sizing, and continuous monitoring of risk metrics "
                "to protect capital while maximizing risk-adjusted returns.")
    
    def _get_agent_backstory(self) -> str:
        """Get the risk management agent's backstory"""
        return ("You are an expert risk manager with extensive experience in quantitative "
                "risk analysis, portfolio optimization, and capital preservation. You have "
                "deep knowledge of various risk measurement techniques including VaR, CVaR, "
                "and stress testing. Your primary responsibility is to ensure that all "
                "trading activities stay within acceptable risk parameters while optimizing "
                "for risk-adjusted returns. You are conservative by nature but understand "
                "the importance of taking calculated risks to achieve superior performance.")
    
    async def process_task(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Process a risk management task"""
        try:
            task_type = task.input_data.get("task_type", "risk_assessment")
            
            if task_type == "calculate_var":
                return await self._calculate_var(task)
            elif task_type == "position_sizing":
                return await self._calculate_position_size(task)
            elif task_type == "risk_metrics":
                return await self._calculate_risk_metrics(task)
            elif task_type == "risk_assessment":
                return await self._comprehensive_risk_assessment(task)
            else:
                raise ValueError(f"Unsupported task type: {task_type}")
                
        except Exception as e:
            self.logger.error(f"Error processing risk management task: {e}")
            raise
    
    async def _calculate_var(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Calculate Value at Risk"""
        portfolio_data = task.input_data.get("portfolio_data", {})
        confidence_level = task.input_data.get("confidence_level", 0.95)
        time_horizon = task.input_data.get("time_horizon", 1)
        method = task.input_data.get("method", "historical")
        
        if not portfolio_data:
            raise ValueError("Portfolio data is required for VaR calculation")
        
        # Use the VaR calculation tool
        var_tool = self.tools[0]  # VaRCalculationTool
        result = var_tool._run(portfolio_data, confidence_level, time_horizon, method)
        
        if "error" in result:
            raise Exception(result["error"])
        
        var_amount = result.get("var_amount", 0)
        portfolio_value = result.get("portfolio_value", 0)
        var_percentage = (var_amount / portfolio_value) * 100 if portfolio_value > 0 else 0
        
        # Check against risk limits
        exceeds_limit = var_percentage > (self.risk_limits["max_daily_var"] * 100)
        
        return {
            "var_calculation": result,
            "var_amount": var_amount,
            "var_percentage": var_percentage,
            "exceeds_risk_limit": exceeds_limit,
            "risk_limit": self.risk_limits["max_daily_var"] * 100,
            "agent_id": self.agent_id,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _calculate_position_size(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Calculate optimal position size"""
        trade_data = task.input_data.get("trade_data", {})
        risk_parameters = task.input_data.get("risk_parameters", {})
        method = task.input_data.get("method", "fixed_fractional")
        
        if not trade_data:
            raise ValueError("Trade data is required for position sizing")
        
        # Use the position sizing tool
        sizing_tool = self.tools[1]  # PositionSizingTool
        result = sizing_tool._run(trade_data, risk_parameters, method)
        
        if "error" in result:
            raise Exception(result["error"])
        
        position_size = result.get("position_size", 0)
        position_value = result.get("position_value", 0)
        account_value = risk_parameters.get("account_value", 100000)
        position_percentage = (position_value / account_value) * 100 if account_value > 0 else 0
        
        # Check against position size limits
        exceeds_limit = position_percentage > (self.risk_limits["max_position_size"] * 100)
        
        return {
            "position_sizing": result,
            "position_size": position_size,
            "position_value": position_value,
            "position_percentage": position_percentage,
            "exceeds_size_limit": exceeds_limit,
            "size_limit": self.risk_limits["max_position_size"] * 100,
            "agent_id": self.agent_id,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _calculate_risk_metrics(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Calculate comprehensive risk metrics"""
        portfolio_data = task.input_data.get("portfolio_data", {})
        benchmark_data = task.input_data.get("benchmark_data")
        
        if not portfolio_data:
            raise ValueError("Portfolio data is required for risk metrics calculation")
        
        # Use the risk metrics tool
        metrics_tool = self.tools[2]  # RiskMetricsTool
        result = metrics_tool._run(portfolio_data, benchmark_data)
        
        if "error" in result:
            raise Exception(result["error"])
        
        risk_metrics = result.get("risk_metrics", {})
        risk_assessment = result.get("risk_assessment", "unknown")
        
        # Check metrics against limits
        sharpe_ratio = risk_metrics.get("sharpe_ratio", 0)
        max_drawdown = risk_metrics.get("max_drawdown", 0)
        
        metrics_within_limits = (
            sharpe_ratio >= self.risk_limits["min_sharpe_ratio"] and
            max_drawdown <= self.risk_limits["max_drawdown"]
        )
        
        return {
            "risk_metrics_result": result,
            "risk_metrics": risk_metrics,
            "risk_assessment": risk_assessment,
            "metrics_within_limits": metrics_within_limits,
            "agent_id": self.agent_id,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _comprehensive_risk_assessment(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Perform comprehensive risk assessment"""
        portfolio_data = task.input_data.get("portfolio_data", {})
        trade_data = task.input_data.get("trade_data")
        risk_parameters = task.input_data.get("risk_parameters", {})
        
        assessment_results = {}
        overall_risk_score = 0
        
        # Calculate VaR if portfolio data is provided
        if portfolio_data:
            try:
                var_task = MultiAgentTask(
                    task_id=f"{task.task_id}_var",
                    description="VaR calculation component",
                    priority=task.priority,
                    required_agents=[AgentRole.RISK_MANAGER],
                    input_data={"task_type": "calculate_var", "portfolio_data": portfolio_data},
                    expected_output={}
                )
                assessment_results["var"] = await self._calculate_var(var_task)
                overall_risk_score += 1 if not assessment_results["var"]["exceeds_risk_limit"] else 0
            except Exception as e:
                self.logger.warning(f"VaR calculation failed: {e}")
        
        # Calculate position sizing if trade data is provided
        if trade_data:
            try:
                sizing_task = MultiAgentTask(
                    task_id=f"{task.task_id}_sizing",
                    description="Position sizing component",
                    priority=task.priority,
                    required_agents=[AgentRole.RISK_MANAGER],
                    input_data={"task_type": "position_sizing", "trade_data": trade_data, "risk_parameters": risk_parameters},
                    expected_output={}
                )
                assessment_results["position_sizing"] = await self._calculate_position_size(sizing_task)
                overall_risk_score += 1 if not assessment_results["position_sizing"]["exceeds_size_limit"] else 0
            except Exception as e:
                self.logger.warning(f"Position sizing failed: {e}")
        
        # Calculate risk metrics
        if portfolio_data:
            try:
                metrics_task = MultiAgentTask(
                    task_id=f"{task.task_id}_metrics",
                    description="Risk metrics component",
                    priority=task.priority,
                    required_agents=[AgentRole.RISK_MANAGER],
                    input_data={"task_type": "risk_metrics", "portfolio_data": portfolio_data},
                    expected_output={}
                )
                assessment_results["risk_metrics"] = await self._calculate_risk_metrics(metrics_task)
                overall_risk_score += 1 if assessment_results["risk_metrics"]["metrics_within_limits"] else 0
            except Exception as e:
                self.logger.warning(f"Risk metrics calculation failed: {e}")
        
        # Calculate overall risk assessment
        total_assessments = len(assessment_results)
        risk_score_percentage = (overall_risk_score / total_assessments) * 100 if total_assessments > 0 else 0
        
        if risk_score_percentage >= 80:
            overall_assessment = "low_risk"
        elif risk_score_percentage >= 60:
            overall_assessment = "moderate_risk"
        else:
            overall_assessment = "high_risk"
        
        return {
            "assessment_type": "comprehensive",
            "components_assessed": total_assessments,
            "assessment_results": assessment_results,
            "overall_risk_score": risk_score_percentage,
            "overall_assessment": overall_assessment,
            "agent_id": self.agent_id,
            "timestamp": datetime.now().isoformat()
        }
