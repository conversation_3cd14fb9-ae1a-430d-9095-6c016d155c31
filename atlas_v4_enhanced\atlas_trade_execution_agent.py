"""
A.T.L.A.S. Trade Execution Agent
Specialized agent for generating 6-point trading recommendations and execution management
"""

import asyncio
import logging
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# CrewAI imports
from crewai.tools import BaseTool

# Core imports
from atlas_multi_agent_core import AtlasBaseAgent, AgentRole, MultiAgentTask
from atlas_trading_core import AtlasTradingEngine
from models import OrderSide, OrderType, Position

logger = logging.getLogger(__name__)

# ============================================================================
# TRADE EXECUTION ENUMS AND MODELS
# ============================================================================

class TradeSignalStrength(Enum):
    """Trade signal strength levels"""
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    VERY_STRONG = "very_strong"

@dataclass
class SixPointTradingRecommendation:
    """6-point trading recommendation structure"""
    symbol: str
    signal_strength: TradeSignalStrength
    entry_price: float
    target_price: float
    stop_loss: float
    position_size: int
    risk_reward_ratio: float
    confidence_score: float
    reasoning: str
    timeframe: str
    expiry_time: datetime
    compliance_checked: bool = False
    execution_priority: str = "normal"

# ============================================================================
# TRADE EXECUTION TOOLS
# ============================================================================

class TradingSignalGeneratorTool(BaseTool):
    """Tool for generating comprehensive trading signals"""
    
    name: str = "signal_generator"
    description: str = "Generates comprehensive trading signals with 6-point analysis"
    
    def __init__(self, trading_engine: AtlasTradingEngine):
        super().__init__()
        self.trading_engine = trading_engine
    
    def _run(self, analysis_data: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """Generate trading signal based on analysis data"""
        try:
            # Extract analysis components
            pattern_data = analysis_data.get("pattern_analysis", {})
            sentiment_data = analysis_data.get("sentiment_analysis", {})
            risk_data = analysis_data.get("risk_analysis", {})
            
            # Generate signal strength
            signal_strength = self._calculate_signal_strength(pattern_data, sentiment_data, risk_data)
            
            # Generate price targets
            current_price = analysis_data.get("current_price", 100.0)
            entry_price, target_price, stop_loss = self._calculate_price_levels(
                current_price, pattern_data, signal_strength
            )
            
            # Calculate position sizing
            position_size = self._calculate_position_size(risk_data, entry_price, stop_loss)
            
            # Calculate risk-reward ratio
            risk_reward_ratio = self._calculate_risk_reward_ratio(entry_price, target_price, stop_loss)
            
            # Generate confidence score
            confidence_score = self._calculate_confidence_score(pattern_data, sentiment_data, risk_data)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(pattern_data, sentiment_data, risk_data, signal_strength)
            
            recommendation = SixPointTradingRecommendation(
                symbol=symbol,
                signal_strength=signal_strength,
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                position_size=position_size,
                risk_reward_ratio=risk_reward_ratio,
                confidence_score=confidence_score,
                reasoning=reasoning,
                timeframe="1D",
                expiry_time=datetime.now() + timedelta(hours=24)
            )
            
            return {
                "recommendation": recommendation.__dict__,
                "signal_generated": True,
                "generation_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def _calculate_signal_strength(self, pattern_data: Dict, sentiment_data: Dict, risk_data: Dict) -> TradeSignalStrength:
        """Calculate overall signal strength"""
        pattern_confidence = pattern_data.get("confidence_score", 0.5)
        sentiment_score = abs(sentiment_data.get("overall_sentiment", 0.0))
        risk_score = 1 - risk_data.get("risk_percentage", 50) / 100
        
        combined_score = (pattern_confidence + sentiment_score + risk_score) / 3
        
        if combined_score >= 0.8:
            return TradeSignalStrength.VERY_STRONG
        elif combined_score >= 0.65:
            return TradeSignalStrength.STRONG
        elif combined_score >= 0.5:
            return TradeSignalStrength.MODERATE
        else:
            return TradeSignalStrength.WEAK
    
    def _calculate_price_levels(self, current_price: float, pattern_data: Dict, 
                               signal_strength: TradeSignalStrength) -> Tuple[float, float, float]:
        """Calculate entry, target, and stop-loss prices"""
        # Base calculations on pattern data if available
        if "entry_price" in pattern_data:
            entry_price = pattern_data["entry_price"]
            target_price = pattern_data.get("target_price", entry_price * 1.05)
            stop_loss = pattern_data.get("stop_loss", entry_price * 0.97)
        else:
            # Default calculations based on current price and signal strength
            entry_price = current_price
            
            if signal_strength == TradeSignalStrength.VERY_STRONG:
                target_multiplier = 1.08
                stop_multiplier = 0.96
            elif signal_strength == TradeSignalStrength.STRONG:
                target_multiplier = 1.06
                stop_multiplier = 0.97
            elif signal_strength == TradeSignalStrength.MODERATE:
                target_multiplier = 1.04
                stop_multiplier = 0.98
            else:
                target_multiplier = 1.02
                stop_multiplier = 0.99
            
            target_price = entry_price * target_multiplier
            stop_loss = entry_price * stop_multiplier
        
        return entry_price, target_price, stop_loss
    
    def _calculate_position_size(self, risk_data: Dict, entry_price: float, stop_loss: float) -> int:
        """Calculate appropriate position size"""
        suggested_size = risk_data.get("position_size", 100)
        
        # Adjust based on risk-reward
        risk_per_share = abs(entry_price - stop_loss)
        if risk_per_share > 0:
            max_risk_amount = 1000  # $1000 max risk per trade
            max_shares = int(max_risk_amount / risk_per_share)
            suggested_size = min(suggested_size, max_shares)
        
        return max(1, suggested_size)
    
    def _calculate_risk_reward_ratio(self, entry_price: float, target_price: float, stop_loss: float) -> float:
        """Calculate risk-reward ratio"""
        potential_profit = abs(target_price - entry_price)
        potential_loss = abs(entry_price - stop_loss)
        
        if potential_loss > 0:
            return round(potential_profit / potential_loss, 2)
        else:
            return 0.0
    
    def _calculate_confidence_score(self, pattern_data: Dict, sentiment_data: Dict, risk_data: Dict) -> float:
        """Calculate overall confidence score"""
        pattern_confidence = pattern_data.get("confidence_score", 0.5)
        sentiment_confidence = sentiment_data.get("confidence_score", 0.5)
        risk_confidence = 1 - (risk_data.get("risk_percentage", 50) / 100)
        
        # Weighted average with pattern analysis having highest weight
        weights = [0.5, 0.3, 0.2]
        scores = [pattern_confidence, sentiment_confidence, risk_confidence]
        
        confidence = sum(w * s for w, s in zip(weights, scores))
        return round(confidence, 3)
    
    def _generate_reasoning(self, pattern_data: Dict, sentiment_data: Dict, 
                          risk_data: Dict, signal_strength: TradeSignalStrength) -> str:
        """Generate human-readable reasoning for the trade recommendation"""
        reasoning_parts = []
        
        # Pattern analysis reasoning
        if pattern_data.get("patterns_detected", 0) > 0:
            reasoning_parts.append(f"Technical pattern analysis shows {pattern_data.get('patterns_detected', 0)} bullish patterns")
        
        # Sentiment reasoning
        sentiment_score = sentiment_data.get("overall_sentiment", 0.0)
        if sentiment_score > 0.3:
            reasoning_parts.append("Market sentiment is positive")
        elif sentiment_score < -0.3:
            reasoning_parts.append("Market sentiment is negative")
        
        # Risk reasoning
        risk_level = risk_data.get("risk_assessment", "moderate")
        reasoning_parts.append(f"Risk assessment indicates {risk_level} risk level")
        
        # Signal strength reasoning
        reasoning_parts.append(f"Overall signal strength is {signal_strength.value}")
        
        return ". ".join(reasoning_parts) + "."

class ComplianceCheckTool(BaseTool):
    """Tool for checking trading compliance and regulations"""
    
    name: str = "compliance_checker"
    description: str = "Checks trading recommendations against compliance rules and regulations"
    
    def _run(self, recommendation: Dict[str, Any], account_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Check recommendation for compliance"""
        try:
            compliance_checks = {}
            all_passed = True
            
            # Position size check
            position_size = recommendation.get("position_size", 0)
            position_value = position_size * recommendation.get("entry_price", 0)
            account_value = account_data.get("account_value", 100000) if account_data else 100000
            
            position_percentage = (position_value / account_value) * 100
            max_position_percentage = 10  # 10% max position size
            
            compliance_checks["position_size"] = {
                "passed": position_percentage <= max_position_percentage,
                "current_percentage": position_percentage,
                "max_allowed": max_position_percentage,
                "details": f"Position represents {position_percentage:.2f}% of account"
            }
            
            if not compliance_checks["position_size"]["passed"]:
                all_passed = False
            
            # Risk-reward ratio check
            risk_reward = recommendation.get("risk_reward_ratio", 0)
            min_risk_reward = 1.5
            
            compliance_checks["risk_reward"] = {
                "passed": risk_reward >= min_risk_reward,
                "current_ratio": risk_reward,
                "min_required": min_risk_reward,
                "details": f"Risk-reward ratio is {risk_reward:.2f}"
            }
            
            if not compliance_checks["risk_reward"]["passed"]:
                all_passed = False
            
            # Confidence score check
            confidence = recommendation.get("confidence_score", 0)
            min_confidence = 0.7
            
            compliance_checks["confidence"] = {
                "passed": confidence >= min_confidence,
                "current_score": confidence,
                "min_required": min_confidence,
                "details": f"Confidence score is {confidence:.3f}"
            }
            
            if not compliance_checks["confidence"]["passed"]:
                all_passed = False
            
            # Market hours check (simplified)
            current_hour = datetime.now().hour
            market_open = 9  # 9 AM
            market_close = 16  # 4 PM
            
            compliance_checks["market_hours"] = {
                "passed": market_open <= current_hour <= market_close,
                "current_hour": current_hour,
                "market_hours": f"{market_open}:00 - {market_close}:00",
                "details": "Trade timing within market hours"
            }
            
            if not compliance_checks["market_hours"]["passed"]:
                all_passed = False
            
            return {
                "compliance_passed": all_passed,
                "compliance_checks": compliance_checks,
                "check_timestamp": datetime.now().isoformat(),
                "violations": [check for check, result in compliance_checks.items() if not result["passed"]]
            }
            
        except Exception as e:
            return {"error": str(e)}

class ExecutionPriorityTool(BaseTool):
    """Tool for determining trade execution priority"""
    
    name: str = "execution_prioritizer"
    description: str = "Determines execution priority based on signal strength and market conditions"
    
    def _run(self, recommendation: Dict[str, Any], market_conditions: Dict[str, Any] = None) -> Dict[str, Any]:
        """Determine execution priority"""
        try:
            signal_strength = recommendation.get("signal_strength", "moderate")
            confidence_score = recommendation.get("confidence_score", 0.5)
            risk_reward_ratio = recommendation.get("risk_reward_ratio", 1.0)
            
            # Calculate priority score
            priority_score = 0
            
            # Signal strength contribution
            strength_scores = {
                "very_strong": 4,
                "strong": 3,
                "moderate": 2,
                "weak": 1
            }
            priority_score += strength_scores.get(signal_strength, 1)
            
            # Confidence contribution
            priority_score += confidence_score * 3
            
            # Risk-reward contribution
            priority_score += min(risk_reward_ratio / 2, 2)
            
            # Market conditions adjustment
            if market_conditions:
                volatility = market_conditions.get("volatility", 0.2)
                if volatility > 0.3:  # High volatility
                    priority_score += 1
                elif volatility < 0.1:  # Low volatility
                    priority_score -= 0.5
            
            # Determine priority level
            if priority_score >= 8:
                priority = "urgent"
            elif priority_score >= 6:
                priority = "high"
            elif priority_score >= 4:
                priority = "normal"
            else:
                priority = "low"
            
            return {
                "execution_priority": priority,
                "priority_score": priority_score,
                "reasoning": f"Priority determined by signal strength ({signal_strength}), confidence ({confidence_score:.2f}), and risk-reward ({risk_reward_ratio:.2f})",
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": str(e)}

# ============================================================================
# TRADE EXECUTION AGENT
# ============================================================================

class AtlasTradeExecutionAgent(AtlasBaseAgent):
    """Specialized agent for trade execution and 6-point recommendations"""
    
    def __init__(self, agent_id: str = None, config: Dict[str, Any] = None):
        agent_id = agent_id or f"trade_executor_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        super().__init__(agent_id, AgentRole.TRADE_EXECUTOR, config)
        
        self.trading_engine = None
        self.execution_thresholds = {
            "min_confidence_score": 0.7,
            "min_risk_reward_ratio": 1.5,
            "max_position_percentage": 10.0
        }
    
    async def _initialize_tools(self):
        """Initialize trade execution specific tools"""
        try:
            # Initialize trading engine
            self.trading_engine = AtlasTradingEngine()
            await self.trading_engine.initialize()
            
            # Initialize execution tools
            self.tools = [
                TradingSignalGeneratorTool(self.trading_engine),
                ComplianceCheckTool(),
                ExecutionPriorityTool()
            ]
            
            self.logger.info(f"Initialized {len(self.tools)} trade execution tools")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize trade execution tools: {e}")
            raise
    
    def _get_agent_goal(self) -> str:
        """Get the trade execution agent's primary goal"""
        return ("Generate precise 6-point trading recommendations with optimal entry, "
                "target, and stop-loss levels, ensure full compliance with trading "
                "regulations, and prioritize execution based on signal strength and "
                "market conditions to maximize trading success.")
    
    def _get_agent_backstory(self) -> str:
        """Get the trade execution agent's backstory"""
        return ("You are an expert trading execution specialist with extensive experience "
                "in generating actionable trading recommendations. You have deep knowledge "
                "of order management, risk controls, and regulatory compliance. Your "
                "specialty is the 6-point trading methodology which provides comprehensive "
                "trade analysis including entry points, targets, stop-losses, position "
                "sizing, risk-reward ratios, and confidence assessments. You are meticulous "
                "about compliance and always ensure that recommendations meet regulatory "
                "requirements and risk management standards.")
    
    async def process_task(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Process a trade execution task"""
        try:
            task_type = task.input_data.get("task_type", "generate_recommendation")
            
            if task_type == "generate_recommendation":
                return await self._generate_trading_recommendation(task)
            elif task_type == "check_compliance":
                return await self._check_compliance(task)
            elif task_type == "prioritize_execution":
                return await self._prioritize_execution(task)
            elif task_type == "comprehensive_execution":
                return await self._comprehensive_execution_analysis(task)
            else:
                raise ValueError(f"Unsupported task type: {task_type}")
                
        except Exception as e:
            self.logger.error(f"Error processing trade execution task: {e}")
            raise
    
    async def _generate_trading_recommendation(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Generate 6-point trading recommendation"""
        analysis_data = task.input_data.get("analysis_data", {})
        symbol = task.input_data.get("symbol", "")
        
        if not symbol:
            raise ValueError("Symbol is required for trading recommendation")
        
        if not analysis_data:
            raise ValueError("Analysis data is required for trading recommendation")
        
        # Use the signal generator tool
        signal_tool = self.tools[0]  # TradingSignalGeneratorTool
        result = signal_tool._run(analysis_data, symbol)
        
        if "error" in result:
            raise Exception(result["error"])
        
        recommendation = result.get("recommendation", {})
        confidence_score = recommendation.get("confidence_score", 0.0)
        
        # Update agent metrics
        self.metrics.confidence_score = (
            (self.metrics.confidence_score * self.metrics.tasks_completed + confidence_score) 
            / (self.metrics.tasks_completed + 1)
        )
        
        # Check if recommendation meets minimum thresholds
        meets_thresholds = (
            confidence_score >= self.execution_thresholds["min_confidence_score"] and
            recommendation.get("risk_reward_ratio", 0) >= self.execution_thresholds["min_risk_reward_ratio"]
        )
        
        return {
            "recommendation_generated": True,
            "recommendation": recommendation,
            "meets_execution_thresholds": meets_thresholds,
            "confidence_score": confidence_score,
            "agent_id": self.agent_id,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _check_compliance(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Check trading recommendation for compliance"""
        recommendation = task.input_data.get("recommendation", {})
        account_data = task.input_data.get("account_data")
        
        if not recommendation:
            raise ValueError("Recommendation is required for compliance check")
        
        # Use the compliance check tool
        compliance_tool = self.tools[1]  # ComplianceCheckTool
        result = compliance_tool._run(recommendation, account_data)
        
        if "error" in result:
            raise Exception(result["error"])
        
        compliance_passed = result.get("compliance_passed", False)
        violations = result.get("violations", [])
        
        return {
            "compliance_checked": True,
            "compliance_passed": compliance_passed,
            "violations_count": len(violations),
            "compliance_result": result,
            "agent_id": self.agent_id,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _prioritize_execution(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Determine execution priority for trading recommendation"""
        recommendation = task.input_data.get("recommendation", {})
        market_conditions = task.input_data.get("market_conditions")
        
        if not recommendation:
            raise ValueError("Recommendation is required for execution prioritization")
        
        # Use the execution priority tool
        priority_tool = self.tools[2]  # ExecutionPriorityTool
        result = priority_tool._run(recommendation, market_conditions)
        
        if "error" in result:
            raise Exception(result["error"])
        
        execution_priority = result.get("execution_priority", "normal")
        priority_score = result.get("priority_score", 0)
        
        return {
            "priority_determined": True,
            "execution_priority": execution_priority,
            "priority_score": priority_score,
            "priority_result": result,
            "agent_id": self.agent_id,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _comprehensive_execution_analysis(self, task: MultiAgentTask) -> Dict[str, Any]:
        """Perform comprehensive execution analysis including all components"""
        analysis_data = task.input_data.get("analysis_data", {})
        symbol = task.input_data.get("symbol", "")
        account_data = task.input_data.get("account_data")
        market_conditions = task.input_data.get("market_conditions")
        
        execution_results = {}
        
        # Generate trading recommendation
        try:
            recommendation_task = MultiAgentTask(
                task_id=f"{task.task_id}_recommendation",
                description="Trading recommendation generation",
                priority=task.priority,
                required_agents=[AgentRole.TRADE_EXECUTOR],
                input_data={"task_type": "generate_recommendation", "analysis_data": analysis_data, "symbol": symbol},
                expected_output={}
            )
            execution_results["recommendation"] = await self._generate_trading_recommendation(recommendation_task)
        except Exception as e:
            self.logger.error(f"Recommendation generation failed: {e}")
            return {"error": f"Failed to generate recommendation: {e}"}
        
        recommendation = execution_results["recommendation"]["recommendation"]
        
        # Check compliance
        try:
            compliance_task = MultiAgentTask(
                task_id=f"{task.task_id}_compliance",
                description="Compliance check",
                priority=task.priority,
                required_agents=[AgentRole.TRADE_EXECUTOR],
                input_data={"task_type": "check_compliance", "recommendation": recommendation, "account_data": account_data},
                expected_output={}
            )
            execution_results["compliance"] = await self._check_compliance(compliance_task)
        except Exception as e:
            self.logger.warning(f"Compliance check failed: {e}")
        
        # Determine execution priority
        try:
            priority_task = MultiAgentTask(
                task_id=f"{task.task_id}_priority",
                description="Execution priority determination",
                priority=task.priority,
                required_agents=[AgentRole.TRADE_EXECUTOR],
                input_data={"task_type": "prioritize_execution", "recommendation": recommendation, "market_conditions": market_conditions},
                expected_output={}
            )
            execution_results["priority"] = await self._prioritize_execution(priority_task)
        except Exception as e:
            self.logger.warning(f"Priority determination failed: {e}")
        
        # Determine overall execution readiness
        recommendation_ready = execution_results["recommendation"]["meets_execution_thresholds"]
        compliance_ready = execution_results.get("compliance", {}).get("compliance_passed", False)
        
        execution_ready = recommendation_ready and compliance_ready
        
        return {
            "analysis_type": "comprehensive_execution",
            "symbol": symbol,
            "execution_ready": execution_ready,
            "execution_results": execution_results,
            "final_recommendation": recommendation if execution_ready else None,
            "agent_id": self.agent_id,
            "timestamp": datetime.now().isoformat()
        }
