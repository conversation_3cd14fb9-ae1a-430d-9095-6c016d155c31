#!/usr/bin/env python3
"""
A.T.L.A.S. Session Manager
Manages aiohttp sessions to prevent unclosed session warnings
"""

import asyncio
import logging
import aiohttp
import weakref
from typing import Dict, Optional, Set
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

class AtlasSessionManager:
    """Manages aiohttp sessions for the A.T.L.A.S. system"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._sessions: Dict[str, aiohttp.ClientSession] = {}
        self._session_refs: Set[weakref.ref] = set()
        self._lock = asyncio.Lock()
        self._initialized = True
        logger.info("Atlas Session Manager initialized")
    
    async def get_session(self, name: str = "default", **kwargs) -> aiohttp.ClientSession:
        """Get or create a named session"""
        async with self._lock:
            if name not in self._sessions or self._sessions[name].closed:
                # Default session configuration
                default_kwargs = {
                    'timeout': aiohttp.ClientTimeout(total=30),
                    'connector': aiohttp.TCPConnector(
                        limit=100,
                        limit_per_host=30,
                        ttl_dns_cache=300,
                        use_dns_cache=True,
                    )
                }
                default_kwargs.update(kwargs)
                
                session = aiohttp.ClientSession(**default_kwargs)
                self._sessions[name] = session
                
                # Keep weak reference for cleanup
                ref = weakref.ref(session, self._cleanup_ref)
                self._session_refs.add(ref)
                
                logger.debug(f"Created new session: {name}")
            
            return self._sessions[name]
    
    def _cleanup_ref(self, ref):
        """Clean up weak reference"""
        self._session_refs.discard(ref)
    
    async def close_session(self, name: str):
        """Close a specific session"""
        async with self._lock:
            if name in self._sessions:
                session = self._sessions[name]
                if not session.closed:
                    await session.close()
                del self._sessions[name]
                logger.debug(f"Closed session: {name}")
    
    async def close_all_sessions(self):
        """Close all sessions"""
        async with self._lock:
            for name, session in list(self._sessions.items()):
                if not session.closed:
                    await session.close()
                    logger.debug(f"Closed session: {name}")
            self._sessions.clear()
            logger.info("All sessions closed")
    
    @asynccontextmanager
    async def session_context(self, name: str = "temp", **kwargs):
        """Context manager for temporary sessions"""
        session = await self.get_session(name, **kwargs)
        try:
            yield session
        finally:
            await self.close_session(name)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close_all_sessions()
    
    def __del__(self):
        """Cleanup on deletion"""
        if hasattr(self, '_sessions'):
            for session in self._sessions.values():
                if not session.closed:
                    logger.warning(f"Session not properly closed: {session}")

# Global session manager instance
session_manager = AtlasSessionManager()

# Convenience functions
async def get_session(name: str = "default", **kwargs) -> aiohttp.ClientSession:
    """Get a managed session"""
    return await session_manager.get_session(name, **kwargs)

async def close_session(name: str):
    """Close a managed session"""
    await session_manager.close_session(name)

async def close_all_sessions():
    """Close all managed sessions"""
    await session_manager.close_all_sessions()

@asynccontextmanager
async def session_context(name: str = "temp", **kwargs):
    """Context manager for temporary sessions"""
    async with session_manager.session_context(name, **kwargs) as session:
        yield session

# Cleanup function for graceful shutdown
async def cleanup_sessions():
    """Cleanup all sessions - call this on application shutdown"""
    try:
        await session_manager.close_all_sessions()
        logger.info("Session cleanup completed")
    except Exception as e:
        logger.error(f"Error during session cleanup: {e}")
